'use client';

import { useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import { usePathname } from 'next/navigation';

/**
 * Hook to ensure wallet connection persistence across page navigation
 *
 * This hook:
 * 1. Checks if wallet is connected on every page change
 * 2. Logs connection status for debugging
 * 3. Stores connection state in localStorage
 * 4. Listens for reconnection events
 */
export function useWalletConnectionPersistence() {
  const { isConnected, address } = useAppKitAccount();
  const pathname = usePathname();

  // Check wallet connection status on every page change
  useEffect(() => {
    // Log connection status for debugging
    console.log('[Wallet Connection] Path changed to:', pathname);
    console.log('[Wallet Connection] isConnected:', isConnected);
    console.log('[Wallet Connection] address:', address);

    // Store connection state in localStorage
    if (isConnected && address) {
      localStorage.setItem('walletConnected', 'true');
      localStorage.setItem('walletAddress', address);
    } else if (!isConnected) {
      // Check if we were previously connected
      const wasConnected = localStorage.getItem('walletConnected') === 'true';

      if (wasConnected) {
        console.log('[Wallet Connection] Wallet was previously connected but is now disconnected');
        // Keep the localStorage items to remember we were connected
        // This allows us to detect disconnections across page changes
      } else {
        // If we weren't connected before and aren't now, clear the localStorage items
        localStorage.removeItem('walletConnected');
        localStorage.removeItem('walletAddress');
      }
    }
  }, [pathname, isConnected, address]);

  // Listen for reconnection events
  useEffect(() => {
    const handleReconnectEvent = () => {
      console.log('[Wallet Connection] Reconnect event received');

      // Here we would trigger a reconnection
      // Since we're using AppKit which handles reconnection internally,
      // we'll just log that we received the event

      // If you need to manually trigger reconnection, you would do it here
      // For example, you might call a function from your wallet provider
    };

    // Add event listener
    window.addEventListener('wallet-reconnect-needed', handleReconnectEvent);

    // Clean up
    return () => {
      window.removeEventListener('wallet-reconnect-needed', handleReconnectEvent);
    };
  }, []);

  return { isConnected, address };
}
