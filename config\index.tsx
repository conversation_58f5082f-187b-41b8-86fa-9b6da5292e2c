'use client';
import { createAppKit } from '@reown/appkit/react'
import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import type { AppKitNetwork } from '@reown/appkit/networks'
import { SolanaAdapter } from '@reown/appkit-adapter-solana'

import {
  mainnet,
  arbitrum,
  sepolia,
  solana,
  cronoszkEVM,
  cronos
} from "@reown/appkit/networks";


export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}
export const metadata = {
    name: 'AppKit',
    description: 'Web3Socials',
    url: 'https://web3socials.fun', // origin must match your domain & subdomain
    icons: ['https://avatars.githubusercontent.com/u/179229932']
  }

// Temporarily reduce networks to test 403 error - you can add more back later
export const networks = [solana] as [AppKitNetwork, ...AppKitNetwork[]];

export const wagmiAdapter = new WagmiAdapter({
  ssr: true,
  projectId,
  networks
});

const solanaWeb3JsAdapter = new SolanaAdapter()

const generalConfig = {
  projectId,
  networks,
  metadata,
  themeMode: 'dark' as const,
  themeVariables: {
    '--w3m-accent': '#000000',
  }
}

createAppKit({
  adapters: [wagmiAdapter,solanaWeb3JsAdapter],
  ...generalConfig,
  features: {
    swaps: false,
    onramp: false,
    email: false,
    socials: false,
    history: false,
    analytics: false,
    allWallets: false
  },
  // Disable features that might cause network requests
  featuredWalletIds: []
})

export const config = wagmiAdapter.wagmiConfig
