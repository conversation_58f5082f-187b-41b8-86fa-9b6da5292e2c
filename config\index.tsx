'use client';

import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import { cronos } from "@reown/appkit/networks";
import { createAppKit } from "@reown/appkit/react";
import { createConfig, http } from 'wagmi';

export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}

export const networks = [
  cronos,
];

// Create wagmi config
export const wagmiConfig = createConfig({
  chains: [cronos],
  transports: {
    [cronos.id]: http(),
  },
});

// Create the adapter
export const wagmiAdapter = new WagmiAdapter({
  projectId,
  networks,
});

createAppKit({
  adapters: [wagmiAdapter],
  networks: [cronos],
  projectId,
  features: {
    swaps: false,
    onramp: false,
    email: false,
    socials: false,
    history: false,
    analytics: false,
    allWallets: false,
  },
});

export const config = wagmiConfig;
