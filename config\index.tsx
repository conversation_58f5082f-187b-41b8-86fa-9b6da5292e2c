'use client';
import { createAppKit } from '@reown/appkit/react'
import { WagmiAdapter } from "@reown/appkit-adapter-wagmi";
import type { AppKitNetwork } from '@reown/appkit/networks'
import {
  mainnet,
  arbitrum,
  sepolia,
  solana,
  cronoszkEVM,
  cronos
} from "@reown/appkit/networks";


export const projectId = process.env.NEXT_PUBLIC_PROJECT_ID;

if (!projectId) {
  throw new Error("Project ID is not defined");
}
export const metadata = {
    name: 'AppKit',
    description: 'Web3Socials',
    url: 'https://web3socials.fun', // origin must match your domain & subdomain
    icons: ['https://avatars.githubusercontent.com/u/179229932']
  }

// Define all networks for multichain support
export const networks = [mainnet, arbitrum, cronos, sepolia, solana, cronoszkEVM] as [AppKitNetwork, ...AppKitNetwork[]];

export const wagmiAdapter = new WagmiAdapter({
  projectId,
  networks
});

const generalConfig = {
  projectId,
  networks,
  metadata,
  themeMode: 'dark' as const,
  themeVariables: {
    '--w3m-accent': '#000000',
  }
}

createAppKit({
  adapters: [wagmiAdapter],
  ...generalConfig,
  features: {
    swaps: false, onramp: false, email: true, socials: false, history: false, analytics: false, allWallets: true
  }
})

export const config = wagmiAdapter.wagmiConfig
