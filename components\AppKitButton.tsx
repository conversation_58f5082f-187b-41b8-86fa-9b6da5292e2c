'use client';

import { useEffect, useRef } from 'react';

import { projectId, metadata, networks, wagmiAdapter } from './config'
import { useAppKitNetwork, useAppKitAccount  } from '@reown/appkit/react'

interface AppKitButtonProps {
  className?: string;
}




export default function AppKitButton({ className }: AppKitButtonProps) {
  const buttonRef = useRef<HTMLDivElement>(null);
   const { chainId } = useAppKitNetwork()
   const { isConnected } = useAppKitAccount() // AppKit hook to get the address and check if the user is connected
     
  // Set the chain attribute on the appkit-button element
  useEffect(() => {
    if (buttonRef.current) {
      const chain = chainId?.toString() || '25'; // Default to Cronos chain ID
      buttonRef.current.setAttribute('chain', chain);
    }
  }, [chainId]);

  // Check for wallet connection status on component mount and when isConnected changes
  useEffect(() => {
    // Log connection status for debugging
    console.log('[AppKitButton] isConnected:', isConnected);

    // Check if we were previously connected (from localStorage)
    const wasConnected = localStorage.getItem('walletConnected') === 'true';

    if (wasConnected && !isConnected) {
      console.log('[AppKitButton] Wallet was previously connected but is now disconnected');

      // Attempt to trigger reconnection by dispatching a custom event
      // This is a non-intrusive way to signal that reconnection should be attempted
      const reconnectEvent = new CustomEvent('wallet-reconnect-needed');
      window.dispatchEvent(reconnectEvent);
    }
  }, [isConnected]);

  return <div ref={buttonRef} className={className}><appkit-button /></div>;
}
