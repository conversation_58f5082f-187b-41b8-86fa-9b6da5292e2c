'use client';

import { ReactNode, useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';
import AppKitButton from '@/components/AppKitButton';

interface SimpleWalletValidatorProps {
  children: ReactNode;
  fallbackContent?: ReactNode;
}

/**
 * A simplified wallet validator that only checks if the wallet is connected,
 * without checking profile status.
 */
export default function SimpleWalletValidator({
  children,
  fallbackContent
}: SimpleWalletValidatorProps) {
  const { isConnected } = useAppKitAccount();

  // Check for wallet reconnection on component mount and when isConnected changes
  useEffect(() => {
    // Check if we were previously connected (from localStorage)
    const wasConnected = localStorage.getItem('walletConnected') === 'true';

    if (wasConnected && !isConnected) {
      console.log('[Discover SimpleWalletValidator] Wallet was previously connected but is now disconnected');

      // Attempt to trigger reconnection by dispatching a custom event
      const reconnectEvent = new CustomEvent('wallet-reconnect-needed');
      window.dispatchEvent(reconnectEvent);
    }
  }, [isConnected]);

  // If wallet is connected, render children
  if (isConnected) {
    return <>{children}</>;
  }

  // If wallet is not connected, render fallback content or default message
  return (
    <div className="bg-black/30 backdrop-blur-md rounded-xl border border-neutral-800 p-6 text-center">
      {fallbackContent || (
        <>
          <h3 className="text-lg font-medium text-neutral-300 mb-2">Connect Your Wallet</h3>
          <p className="text-neutral-400 mb-4">
            Please connect your wallet to view and interact with profiles.
          </p>
          <div className="flex justify-center">
            <AppKitButton />
          </div>
        </>
      )}
    </div>
  );
}
